-- Simple migration to add missing fields to rooms table
-- This version keeps the existing images column as TEXT[] and just adds missing fields

-- Add missing columns to rooms table
ALTER TABLE public.rooms 
ADD COLUMN IF NOT EXISTS bed_type TEXT DEFAULT 'Queen Bed',
ADD COLUMN IF NOT EXISTS size_sqm DECIMAL(5,2) DEFAULT 25.0,
ADD COLUMN IF NOT EXISTS is_available BOOLEAN DEFAULT true NOT NULL;

-- Update the updated_at timestamp for all rooms
UPDATE public.rooms SET updated_at = NOW();

-- Create indexes on new columns for better query performance
CREATE INDEX IF NOT EXISTS idx_rooms_room_type ON public.rooms(room_type);
CREATE INDEX IF NOT EXISTS idx_rooms_status ON public.rooms(status);
CREATE INDEX IF NOT EXISTS idx_rooms_is_available ON public.rooms(is_available);

-- Update any existing rooms to have proper is_available status based on their status
UPDATE public.rooms 
SET is_available = CASE 
  WHEN status = 'available' THEN true 
  ELSE false 
END;

-- Show the updated schema
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'rooms' 
AND table_schema = 'public'
ORDER BY ordinal_position;
